import { NextRequest, NextResponse } from 'next/server';
import { createServerSupabase, verifySession } from '@/lib/supabase-server';
import type { CreateTestimonialData } from '@/types/database';

// GET /api/testimonials - Get testimonials with filtering
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const supabase = createServerSupabase();

    // Parse query parameters
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const featured = searchParams.get('featured') === 'true';
    const tripId = searchParams.get('tripId');
    const includeUnapproved = searchParams.get('includeUnapproved') === 'true';

    // Check if user is admin for unapproved testimonials
    let isAdmin = false;
    if (includeUnapproved) {
      const session = await verifySession();
      if (session) {
        const { data: user } = await supabase
          .from('users')
          .select('role')
          .eq('id', session.user.id)
          .single();
        isAdmin = user?.role === 'admin';
      }
    }

    // Build query
    let query = supabase
      .from('testimonials')
      .select(`
        *,
        user:users(id, full_name),
        trip:trips(id, title, destination)
      `, { count: 'exact' });

    // Apply filters
    if (!includeUnapproved || !isAdmin) {
      query = query.eq('is_approved', true);
    }

    if (featured) {
      query = query.eq('is_featured', true);
    }

    if (tripId) {
      query = query.eq('trip_id', tripId);
    }

    // Apply pagination
    const from = (page - 1) * limit;
    const to = from + limit - 1;
    query = query.range(from, to);

    // Order by featured first, then by created_at
    query = query.order('is_featured', { ascending: false })
                 .order('created_at', { ascending: false });

    const { data: testimonials, error, count } = await query;

    if (error) {
      console.error('Error fetching testimonials:', error);
      return NextResponse.json(
        { error: 'Failed to fetch testimonials' },
        { status: 500 }
      );
    }

    const totalPages = Math.ceil((count || 0) / limit);

    return NextResponse.json({
      data: testimonials,
      pagination: {
        page,
        limit,
        total: count || 0,
        totalPages,
      },
    });
  } catch (error) {
    console.error('Error in GET /api/testimonials:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// POST /api/testimonials - Create a new testimonial
export async function POST(request: NextRequest) {
  try {
    const body: CreateTestimonialData = await request.json();
    const supabase = createServerSupabase();

    // Validate required fields
    const requiredFields = ['name', 'rating', 'content'];
    for (const field of requiredFields) {
      if (!body[field as keyof CreateTestimonialData]) {
        return NextResponse.json(
          { error: `Missing required field: ${field}` },
          { status: 400 }
        );
      }
    }

    // Validate rating
    if (body.rating < 1 || body.rating > 5) {
      return NextResponse.json(
        { error: 'Rating must be between 1 and 5' },
        { status: 400 }
      );
    }

    // Check if user is authenticated (optional)
    const session = await verifySession();
    let userId = null;
    let isAdmin = false;

    if (session) {
      userId = session.user.id;
      const { data: user } = await supabase
        .from('users')
        .select('role')
        .eq('id', session.user.id)
        .single();
      isAdmin = user?.role === 'admin';
    }

    // Validate trip exists if trip_id is provided
    if (body.trip_id) {
      const { data: trip, error: tripError } = await supabase
        .from('trips')
        .select('id')
        .eq('id', body.trip_id)
        .single();

      if (tripError || !trip) {
        return NextResponse.json(
          { error: 'Trip not found' },
          { status: 404 }
        );
      }
    }

    // Create testimonial
    const { data: testimonial, error } = await supabase
      .from('testimonials')
      .insert({
        ...body,
        user_id: userId,
        is_approved: isAdmin, // Auto-approve if admin
        is_featured: false, // Admin can set this later
      })
      .select(`
        *,
        user:users(id, full_name),
        trip:trips(id, title, destination)
      `)
      .single();

    if (error) {
      console.error('Error creating testimonial:', error);
      return NextResponse.json(
        { error: 'Failed to create testimonial' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      data: testimonial,
      message: isAdmin ? 'Testimonial created and approved' : 'Testimonial submitted for review',
    }, { status: 201 });
  } catch (error) {
    console.error('Error in POST /api/testimonials:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
