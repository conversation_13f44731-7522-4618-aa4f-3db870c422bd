import { Metadata } from 'next'
import { Suspense } from 'react'
import Header from '@/components/layout/Header'
import Footer from '@/components/layout/Footer'
import SearchResults from '@/components/search/SearchResults'
import LoadingSpinner from '@/components/ui/LoadingSpinner'

export const metadata: Metadata = {
  title: 'All Trips - Positive7 Educational Tours',
  description: 'Browse all our educational tours and adventures. Find the perfect trip for your group from our collection of amazing destinations across India.',
  keywords: 'educational trips, student tours, group travel, adventure tours, Positive7, Ahmedabad'
}

export default function TripsPage() {
  return (
    <>
      <Header />
      <main className="flex-1">
        <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-green-50">
          <div className="max-w-7xl mx-auto px-4 py-8">
            <Suspense fallback={
              <div className="flex items-center justify-center min-h-[400px]">
                <LoadingSpinner size="lg" />
              </div>
            }>
              <SearchResults />
            </Suspense>
          </div>
        </div>
      </main>
      <Footer />
    </>
  )
}
