import { Metadata } from 'next'
import Header from '@/components/layout/Header'
import Footer from '@/components/layout/Footer'
import TripsClient from '@/components/trips/TripsClient'
import { createServerSupabase } from '@/lib/supabase-server'

export const metadata: Metadata = {
  title: 'All Trips - Positive7 Educational Tours',
  description: 'Browse all our educational tours and adventures. Find the perfect trip for your group from our collection of amazing destinations across India.',
  keywords: 'educational trips, student tours, group travel, adventure tours, Positive7, Ahmedabad'
}

export default async function TripsPage() {
  // Fetch trips data server-side
  const supabase = createServerSupabase()

  const { data: trips, error } = await supabase
    .from('trips')
    .select(`
      id,
      title,
      slug,
      destination,
      duration_days,
      price_per_person,
      difficulty,
      featured_image_url,
      description,
      is_featured,
      is_active
    `)
    .eq('is_active', true)
    .order('created_at', { ascending: false })

  if (error) {
    console.error('Error fetching trips:', error)
  }

  return (
    <>
      <Header />
      <main className="flex-1">
        <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-green-50">
          <div className="max-w-7xl mx-auto px-4 py-8">
            <TripsClient initialTrips={trips || []} />
          </div>
        </div>
      </main>
      <Footer />
    </>
  )
}
