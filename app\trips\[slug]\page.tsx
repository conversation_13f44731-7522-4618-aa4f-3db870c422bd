import { notFound } from 'next/navigation'
import { createServerSupabase } from '@/lib/supabase-server'
import TripDetailClient from '@/components/trips/TripDetailClient'
import type { Trip } from '@/types/database'

interface TripDetailPageProps {
  params: Promise<{
    slug: string
  }>
}

export default async function TripDetailPage({ params }: TripDetailPageProps) {
  const { slug } = await params;
  const supabase = createServerSupabase();

  // Fetch trip data from database
  const { data: trip, error } = await supabase
    .from('trips')
    .select(`
      *,
      trip_images(*),
      testimonials(
        id,
        name,
        rating,
        title,
        content,
        image_url,
        created_at
      )
    `)
    .eq('slug', slug)
    .eq('is_active', true)
    .single();

  if (error || !trip) {
    notFound();
  }

  // Fetch related trips
  const { data: relatedTrips } = await supabase
    .from('trips')
    .select(`
      id,
      title,
      slug,
      description,
      destination,
      duration_days,
      price_per_person,
      difficulty,
      featured_image_url
    `)
    .eq('is_active', true)
    .neq('id', trip.id)
    .limit(3);

  return <TripDetailClient trip={trip} relatedTrips={relatedTrips || []} />;
}
