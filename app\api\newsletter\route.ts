import { NextRequest, NextResponse } from 'next/server';
import { createServerSupabase, verifySession } from '@/lib/supabase-server';

// GET /api/newsletter - Get newsletter subscriptions (Admin only)
export async function GET(request: NextRequest) {
  try {
    const session = await verifySession('admin');
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const supabase = createServerSupabase();

    // Parse query parameters
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const isActive = searchParams.get('isActive');
    const search = searchParams.get('search');

    // Build query
    let query = supabase
      .from('newsletter_subscriptions')
      .select('*', { count: 'exact' });

    // Apply filters
    if (isActive !== null) {
      query = query.eq('is_active', isActive === 'true');
    }

    if (search) {
      query = query.or(`email.ilike.%${search}%,name.ilike.%${search}%`);
    }

    // Apply pagination
    const from = (page - 1) * limit;
    const to = from + limit - 1;
    query = query.range(from, to);

    // Order by subscribed_at descending
    query = query.order('subscribed_at', { ascending: false });

    const { data: subscriptions, error, count } = await query;

    if (error) {
      console.error('Error fetching newsletter subscriptions:', error);
      return NextResponse.json(
        { error: 'Failed to fetch newsletter subscriptions' },
        { status: 500 }
      );
    }

    const totalPages = Math.ceil((count || 0) / limit);

    return NextResponse.json({
      data: subscriptions,
      pagination: {
        page,
        limit,
        total: count || 0,
        totalPages,
      },
    });
  } catch (error) {
    console.error('Error in GET /api/newsletter:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// POST /api/newsletter - Subscribe to newsletter
export async function POST(request: NextRequest) {
  try {
    const { email, name } = await request.json();

    if (!email) {
      return NextResponse.json(
        { error: 'Email is required' },
        { status: 400 }
      );
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return NextResponse.json(
        { error: 'Invalid email format' },
        { status: 400 }
      );
    }

    const supabase = createServerSupabase();

    // Check if email already exists
    const { data: existingSubscription, error: checkError } = await supabase
      .from('newsletter_subscriptions')
      .select('*')
      .eq('email', email.toLowerCase())
      .single();

    if (checkError && checkError.code !== 'PGRST116') {
      console.error('Error checking existing subscription:', checkError);
      return NextResponse.json(
        { error: 'Failed to check subscription status' },
        { status: 500 }
      );
    }

    if (existingSubscription) {
      if (existingSubscription.is_active) {
        return NextResponse.json(
          { error: 'Email is already subscribed to our newsletter' },
          { status: 400 }
        );
      } else {
        // Reactivate subscription
        const { data: subscription, error } = await supabase
          .from('newsletter_subscriptions')
          .update({
            is_active: true,
            name: name || existingSubscription.name,
            subscribed_at: new Date().toISOString(),
            unsubscribed_at: null,
          })
          .eq('id', existingSubscription.id)
          .select()
          .single();

        if (error) {
          console.error('Error reactivating subscription:', error);
          return NextResponse.json(
            { error: 'Failed to reactivate subscription' },
            { status: 500 }
          );
        }

        return NextResponse.json({
          data: subscription,
          message: 'Successfully resubscribed to newsletter!',
        });
      }
    }

    // Create new subscription
    const { data: subscription, error } = await supabase
      .from('newsletter_subscriptions')
      .insert({
        email: email.toLowerCase(),
        name: name || null,
        is_active: true,
      })
      .select()
      .single();

    if (error) {
      console.error('Error creating subscription:', error);
      return NextResponse.json(
        { error: 'Failed to subscribe to newsletter' },
        { status: 500 }
      );
    }

    // TODO: Send welcome email
    // TODO: Add to email marketing platform (Mailchimp, SendGrid, etc.)

    return NextResponse.json({
      data: subscription,
      message: 'Successfully subscribed to newsletter!',
    }, { status: 201 });
  } catch (error) {
    console.error('Error in POST /api/newsletter:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// DELETE /api/newsletter - Unsubscribe from newsletter
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const email = searchParams.get('email');
    const token = searchParams.get('token'); // For unsubscribe links

    if (!email) {
      return NextResponse.json(
        { error: 'Email is required' },
        { status: 400 }
      );
    }

    const supabase = createServerSupabase();

    // TODO: Validate unsubscribe token if provided

    // Find and update subscription
    const { data: subscription, error } = await supabase
      .from('newsletter_subscriptions')
      .update({
        is_active: false,
        unsubscribed_at: new Date().toISOString(),
      })
      .eq('email', email.toLowerCase())
      .eq('is_active', true)
      .select()
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        return NextResponse.json(
          { error: 'Email not found or already unsubscribed' },
          { status: 404 }
        );
      }
      console.error('Error unsubscribing:', error);
      return NextResponse.json(
        { error: 'Failed to unsubscribe' },
        { status: 500 }
      );
    }

    // TODO: Remove from email marketing platform
    // TODO: Send unsubscribe confirmation email

    return NextResponse.json({
      message: 'Successfully unsubscribed from newsletter',
    });
  } catch (error) {
    console.error('Error in DELETE /api/newsletter:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
