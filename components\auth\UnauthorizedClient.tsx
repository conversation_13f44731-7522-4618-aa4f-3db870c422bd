'use client'

import Link from 'next/link'
import { Shield, Home, ArrowLeft } from 'lucide-react'
import Button from '@/components/ui/Button'

export default function UnauthorizedClient() {
  const handleGoBack = () => {
    if (typeof window !== 'undefined') {
      window.history.back()
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-red-50 to-orange-50 flex items-center justify-center p-4">
      <div className="max-w-md w-full bg-white rounded-2xl shadow-xl p-8 text-center">
        {/* Icon */}
        <div className="w-20 h-20 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-6">
          <Shield className="w-10 h-10 text-red-600" />
        </div>

        {/* Title */}
        <h1 className="text-3xl font-bold text-gray-900 mb-4">
          Access Denied
        </h1>

        {/* Description */}
        <p className="text-gray-600 mb-8 leading-relaxed">
          You don't have permission to access this page. This area is restricted to authorized users only.
        </p>

        {/* Action Buttons */}
        <div className="space-y-4">
          <Link href="/">
            <Button className="w-full bg-gradient-to-r from-blue-600 to-green-600 hover:from-blue-700 hover:to-green-700">
              <Home className="w-4 h-4 mr-2" />
              Go to Homepage
            </Button>
          </Link>

          <button
            onClick={handleGoBack}
            className="w-full px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors flex items-center justify-center"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Go Back
          </button>
        </div>

        {/* Help Text */}
        <div className="mt-8 p-4 bg-blue-50 rounded-lg">
          <h3 className="text-sm font-medium text-blue-900 mb-2">Need Access?</h3>
          <p className="text-sm text-blue-700">
            If you believe you should have access to this page, please contact our support team at{' '}
            <a href="mailto:<EMAIL>" className="font-medium underline">
              <EMAIL>
            </a>
          </p>
        </div>
      </div>
    </div>
  )
}
