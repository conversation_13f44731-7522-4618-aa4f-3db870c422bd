{"name": "positive7-tourism-website", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit", "db:generate-types": "supabase gen types typescript --project-id $NEXT_PUBLIC_SUPABASE_PROJECT_ID --schema public > types/supabase.ts", "db:reset": "supabase db reset", "db:push": "supabase db push", "db:pull": "supabase db pull"}, "dependencies": {"@supabase/auth-helpers-nextjs": "^0.8.7", "@supabase/auth-helpers-react": "^0.4.2", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.38.4", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "autoprefixer": "^10.0.1", "clsx": "^2.0.0", "framer-motion": "^10.16.16", "lucide-react": "^0.294.0", "next": "14.0.4", "postcss": "^8", "react": "^18", "react-dom": "^18", "react-hook-form": "^7.48.2", "react-intersection-observer": "^9.16.0", "recharts": "^2.15.3", "tailwind-merge": "^2.2.0", "tailwindcss": "^3.3.0", "typescript": "^5", "web-vitals": "^5.0.1", "zod": "^3.22.4"}, "devDependencies": {"@tailwindcss/forms": "^0.5.7", "@tailwindcss/typography": "^0.5.10", "@types/eslint": "^8", "eslint": "^8", "eslint-config-next": "14.0.4", "supabase": "^1.123.4"}, "engines": {"node": ">=18.0.0"}}