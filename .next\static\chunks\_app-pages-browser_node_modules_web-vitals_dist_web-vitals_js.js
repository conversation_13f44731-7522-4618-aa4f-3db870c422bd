"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_web-vitals_dist_web-vitals_js"],{

/***/ "(app-pages-browser)/./node_modules/web-vitals/dist/web-vitals.js":
/*!****************************************************!*\
  !*** ./node_modules/web-vitals/dist/web-vitals.js ***!
  \****************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CLSThresholds: function() { return /* binding */ T; },\n/* harmony export */   FCPThresholds: function() { return /* binding */ b; },\n/* harmony export */   INPThresholds: function() { return /* binding */ N; },\n/* harmony export */   LCPThresholds: function() { return /* binding */ x; },\n/* harmony export */   TTFBThresholds: function() { return /* binding */ $; },\n/* harmony export */   onCLS: function() { return /* binding */ E; },\n/* harmony export */   onFCP: function() { return /* binding */ P; },\n/* harmony export */   onINP: function() { return /* binding */ S; },\n/* harmony export */   onLCP: function() { return /* binding */ O; },\n/* harmony export */   onTTFB: function() { return /* binding */ H; }\n/* harmony export */ });\nlet e=-1;const t=t=>{addEventListener(\"pageshow\",(n=>{n.persisted&&(e=n.timeStamp,t(n))}),!0)},n=(e,t,n,i)=>{let o,s;return r=>{t.value>=0&&(r||i)&&(s=t.value-(o??0),(s||void 0===o)&&(o=t.value,t.delta=s,t.rating=((e,t)=>e>t[1]?\"poor\":e>t[0]?\"needs-improvement\":\"good\")(t.value,n),e(t)))}},i=e=>{requestAnimationFrame((()=>requestAnimationFrame((()=>e()))))},o=()=>{const e=performance.getEntriesByType(\"navigation\")[0];if(e&&e.responseStart>0&&e.responseStart<performance.now())return e},s=()=>{const e=o();return e?.activationStart??0},r=(t,n=-1)=>{const i=o();let r=\"navigate\";e>=0?r=\"back-forward-cache\":i&&(document.prerendering||s()>0?r=\"prerender\":document.wasDiscarded?r=\"restore\":i.type&&(r=i.type.replace(/_/g,\"-\")));return{name:t,value:n,rating:\"good\",delta:0,entries:[],id:`v5-${Date.now()}-${Math.floor(8999999999999*Math.random())+1e12}`,navigationType:r}},c=new WeakMap;function a(e,t){return c.get(e)||c.set(e,new t),c.get(e)}class d{t;i=0;o=[];h(e){if(e.hadRecentInput)return;const t=this.o[0],n=this.o.at(-1);this.i&&t&&n&&e.startTime-n.startTime<1e3&&e.startTime-t.startTime<5e3?(this.i+=e.value,this.o.push(e)):(this.i=e.value,this.o=[e]),this.t?.(e)}}const h=(e,t,n={})=>{try{if(PerformanceObserver.supportedEntryTypes.includes(e)){const i=new PerformanceObserver((e=>{Promise.resolve().then((()=>{t(e.getEntries())}))}));return i.observe({type:e,buffered:!0,...n}),i}}catch{}},f=e=>{let t=!1;return()=>{t||(e(),t=!0)}};let u=-1;const l=()=>\"hidden\"!==document.visibilityState||document.prerendering?1/0:0,m=e=>{\"hidden\"===document.visibilityState&&u>-1&&(u=\"visibilitychange\"===e.type?e.timeStamp:0,p())},g=()=>{addEventListener(\"visibilitychange\",m,!0),addEventListener(\"prerenderingchange\",m,!0)},p=()=>{removeEventListener(\"visibilitychange\",m,!0),removeEventListener(\"prerenderingchange\",m,!0)},v=()=>{if(u<0){const e=s(),n=document.prerendering?void 0:globalThis.performance.getEntriesByType(\"visibility-state\").filter((t=>\"hidden\"===t.name&&t.startTime>e))[0]?.startTime;u=n??l(),g(),t((()=>{setTimeout((()=>{u=l(),g()}))}))}return{get firstHiddenTime(){return u}}},y=e=>{document.prerendering?addEventListener(\"prerenderingchange\",(()=>e()),!0):e()},b=[1800,3e3],P=(e,o={})=>{y((()=>{const c=v();let a,d=r(\"FCP\");const f=h(\"paint\",(e=>{for(const t of e)\"first-contentful-paint\"===t.name&&(f.disconnect(),t.startTime<c.firstHiddenTime&&(d.value=Math.max(t.startTime-s(),0),d.entries.push(t),a(!0)))}));f&&(a=n(e,d,b,o.reportAllChanges),t((t=>{d=r(\"FCP\"),a=n(e,d,b,o.reportAllChanges),i((()=>{d.value=performance.now()-t.timeStamp,a(!0)}))})))}))},T=[.1,.25],E=(e,o={})=>{P(f((()=>{let s,c=r(\"CLS\",0);const f=a(o,d),u=e=>{for(const t of e)f.h(t);f.i>c.value&&(c.value=f.i,c.entries=f.o,s())},l=h(\"layout-shift\",u);l&&(s=n(e,c,T,o.reportAllChanges),document.addEventListener(\"visibilitychange\",(()=>{\"hidden\"===document.visibilityState&&(u(l.takeRecords()),s(!0))})),t((()=>{f.i=0,c=r(\"CLS\",0),s=n(e,c,T,o.reportAllChanges),i((()=>s()))})),setTimeout(s))})))};let _=0,L=1/0,M=0;const C=e=>{for(const t of e)t.interactionId&&(L=Math.min(L,t.interactionId),M=Math.max(M,t.interactionId),_=M?(M-L)/7+1:0)};let I;const w=()=>I?_:performance.interactionCount??0,F=()=>{\"interactionCount\"in performance||I||(I=h(\"event\",C,{type:\"event\",buffered:!0,durationThreshold:0}))};let k=0;class A{u=[];l=new Map;m;p;v(){k=w(),this.u.length=0,this.l.clear()}P(){const e=Math.min(this.u.length-1,Math.floor((w()-k)/50));return this.u[e]}h(e){if(this.m?.(e),!e.interactionId&&\"first-input\"!==e.entryType)return;const t=this.u.at(-1);let n=this.l.get(e.interactionId);if(n||this.u.length<10||e.duration>t.T){if(n?e.duration>n.T?(n.entries=[e],n.T=e.duration):e.duration===n.T&&e.startTime===n.entries[0].startTime&&n.entries.push(e):(n={id:e.interactionId,entries:[e],T:e.duration},this.l.set(n.id,n),this.u.push(n)),this.u.sort(((e,t)=>t.T-e.T)),this.u.length>10){const e=this.u.splice(10);for(const t of e)this.l.delete(t.id)}this.p?.(n)}}}const B=e=>{const t=globalThis.requestIdleCallback||setTimeout;\"hidden\"===document.visibilityState?e():(t(e=f(e)),document.addEventListener(\"visibilitychange\",e,{once:!0}))},N=[200,500],S=(e,i={})=>{globalThis.PerformanceEventTiming&&\"interactionId\"in PerformanceEventTiming.prototype&&y((()=>{F();let o,s=r(\"INP\");const c=a(i,A),d=e=>{B((()=>{for(const t of e)c.h(t);const t=c.P();t&&t.T!==s.value&&(s.value=t.T,s.entries=t.entries,o())}))},f=h(\"event\",d,{durationThreshold:i.durationThreshold??40});o=n(e,s,N,i.reportAllChanges),f&&(f.observe({type:\"first-input\",buffered:!0}),document.addEventListener(\"visibilitychange\",(()=>{\"hidden\"===document.visibilityState&&(d(f.takeRecords()),o(!0))})),t((()=>{c.v(),s=r(\"INP\"),o=n(e,s,N,i.reportAllChanges)})))}))};class q{m;h(e){this.m?.(e)}}const x=[2500,4e3],O=(e,o={})=>{y((()=>{const c=v();let d,u=r(\"LCP\");const l=a(o,q),m=e=>{o.reportAllChanges||(e=e.slice(-1));for(const t of e)l.h(t),t.startTime<c.firstHiddenTime&&(u.value=Math.max(t.startTime-s(),0),u.entries=[t],d())},g=h(\"largest-contentful-paint\",m);if(g){d=n(e,u,x,o.reportAllChanges);const s=f((()=>{m(g.takeRecords()),g.disconnect(),d(!0)}));for(const e of[\"keydown\",\"click\",\"visibilitychange\"])addEventListener(e,(()=>B(s)),{capture:!0,once:!0});t((t=>{u=r(\"LCP\"),d=n(e,u,x,o.reportAllChanges),i((()=>{u.value=performance.now()-t.timeStamp,d(!0)}))}))}}))},$=[800,1800],D=e=>{document.prerendering?y((()=>D(e))):\"complete\"!==document.readyState?addEventListener(\"load\",(()=>D(e)),!0):setTimeout(e)},H=(e,i={})=>{let c=r(\"TTFB\"),a=n(e,c,$,i.reportAllChanges);D((()=>{const d=o();d&&(c.value=Math.max(d.responseStart-s(),0),c.entries=[d],a(!0),t((()=>{c=r(\"TTFB\",0),a=n(e,c,$,i.reportAllChanges),a(!0)})))}))};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/web-vitals/dist/web-vitals.js\n"));

/***/ })

}]);