'use client'

import Link from 'next/link'
import { WifiOff, RefreshCw, Home, Search, Phone } from 'lucide-react'
import Button from '@/components/ui/Button'

export default function OfflineClient() {
  const handleRefresh = () => {
    if (typeof window !== 'undefined') {
      window.location.reload()
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-green-50 flex items-center justify-center p-4">
      <div className="max-w-md w-full bg-white rounded-2xl shadow-xl p-8 text-center">
        {/* Offline Icon */}
        <div className="w-20 h-20 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-6">
          <WifiOff className="w-10 h-10 text-gray-400" />
        </div>

        {/* Title */}
        <h1 className="text-2xl font-bold text-gray-900 mb-4">
          You're Offline
        </h1>

        {/* Description */}
        <p className="text-gray-600 mb-8 leading-relaxed">
          It looks like you've lost your internet connection. Don't worry - you can still browse 
          some content that we've saved for you, or try reconnecting.
        </p>

        {/* Action Buttons */}
        <div className="space-y-4">
          <Button 
            onClick={handleRefresh}
            className="w-full bg-gradient-to-r from-blue-600 to-green-600 hover:from-blue-700 hover:to-green-700"
          >
            <RefreshCw className="w-4 h-4 mr-2" />
            Try Again
          </Button>

          <div className="grid grid-cols-2 gap-3">
            <Link href="/">
              <Button variant="outline" className="w-full">
                <Home className="w-4 h-4 mr-2" />
                Home
              </Button>
            </Link>
            <Link href="/search">
              <Button variant="outline" className="w-full">
                <Search className="w-4 h-4 mr-2" />
                Browse
              </Button>
            </Link>
          </div>
        </div>

        {/* Offline Features */}
        <div className="mt-8 p-4 bg-blue-50 rounded-lg">
          <h3 className="font-semibold text-gray-900 mb-2">Available Offline:</h3>
          <ul className="text-sm text-gray-600 space-y-1">
            <li>• Previously viewed trip details</li>
            <li>• Saved bookings and preferences</li>
            <li>• Contact information</li>
            <li>• Company information</li>
          </ul>
        </div>

        {/* Emergency Contact */}
        <div className="mt-6 p-4 bg-green-50 rounded-lg">
          <h3 className="font-semibold text-gray-900 mb-2 flex items-center justify-center">
            <Phone className="w-4 h-4 mr-2" />
            Need Help?
          </h3>
          <p className="text-sm text-gray-600 mb-2">
            Call us directly for immediate assistance:
          </p>
          <a 
            href="tel:+919825123456" 
            className="text-blue-600 font-semibold hover:text-blue-700"
          >
            +91 98251 23456
          </a>
        </div>

        {/* Tips */}
        <div className="mt-6 text-xs text-gray-500">
          <p>💡 Tip: This page works offline too! Bookmark it for future reference.</p>
        </div>
      </div>
    </div>
  )
}
