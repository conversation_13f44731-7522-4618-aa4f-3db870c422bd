'use client'

import { useState, useEffect, useMemo } from 'react'
import { useSearchParams, useRouter } from 'next/navigation'
import { motion } from 'framer-motion'
import Image from 'next/image'
import Link from 'next/link'
import {
  Search,
  Filter,
  SlidersHorizontal,
  MapPin,
  Calendar,
  Users,
  Star,
  Clock,
  Grid3X3,
  List,
  ArrowUpDown,
  X
} from 'lucide-react'
import Button from '@/components/ui/Button'
import { createClientSupabase } from '@/lib/supabase-client'
import type { Trip } from '@/types/database'

interface TripSearchResult {
  id: string
  title: string
  slug: string
  destination: string
  duration_days: number
  price_per_person: number
  difficulty: 'easy' | 'moderate' | 'challenging' | 'extreme'
  featured_image_url: string | null
  description: string | null
  is_featured: boolean
  is_active: boolean
}

const DIFFICULTIES = ['All', 'easy', 'moderate', 'challenging', 'extreme']
const PRICE_RANGES = [
  { label: 'All Prices', min: 0, max: Infinity },
  { label: 'Under ₹25,000', min: 0, max: 25000 },
  { label: '₹25,000 - ₹30,000', min: 25000, max: 30000 },
  { label: 'Above ₹30,000', min: 30000, max: Infinity }
]

const SORT_OPTIONS = [
  { value: 'relevance', label: 'Most Relevant' },
  { value: 'price-low', label: 'Price: Low to High' },
  { value: 'price-high', label: 'Price: High to Low' },
  { value: 'duration', label: 'Duration' }
]

export default function SearchResults() {
  const searchParams = useSearchParams()
  const router = useRouter()
  const supabase = createClientSupabase()

  const [searchQuery, setSearchQuery] = useState(searchParams.get('q') || '')
  const [selectedDifficulty, setSelectedDifficulty] = useState('All')
  const [selectedPriceRange, setSelectedPriceRange] = useState(0)
  const [sortBy, setSortBy] = useState('relevance')
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid')
  const [showFilters, setShowFilters] = useState(false)
  const [trips, setTrips] = useState<TripSearchResult[]>([])
  const [loading, setLoading] = useState(true)

  // Fetch trips from database
  useEffect(() => {
    const fetchTrips = async () => {
      setLoading(true)
      console.log('SearchResults: Starting to fetch trips...')
      console.log('SearchResults: Supabase client:', supabase)

      try {
        console.log('SearchResults: Making database query...')
        const { data, error } = await supabase
          .from('trips')
          .select(`
            id,
            title,
            slug,
            destination,
            duration_days,
            price_per_person,
            difficulty,
            featured_image_url,
            description,
            is_featured,
            is_active
          `)
          .eq('is_active', true)
          .order('created_at', { ascending: false })

        console.log('SearchResults: Database response:', { data, error })

        if (error) {
          console.error('SearchResults: Database error:', error)
          throw error
        }

        console.log('SearchResults: Successfully fetched', data?.length || 0, 'trips')
        setTrips(data || [])
      } catch (error) {
        console.error('SearchResults: Error fetching trips:', error)
        setTrips([])
      } finally {
        setLoading(false)
      }
    }

    fetchTrips()
  }, [supabase])

  // Filter and sort trips
  const filteredTrips = useMemo(() => {
    let filtered = trips

    // Search filter
    if (searchQuery) {
      filtered = filtered.filter(trip =>
        trip.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        trip.destination.toLowerCase().includes(searchQuery.toLowerCase()) ||
        (trip.description && trip.description.toLowerCase().includes(searchQuery.toLowerCase()))
      )
    }

    // Difficulty filter
    if (selectedDifficulty !== 'All') {
      filtered = filtered.filter(trip => trip.difficulty === selectedDifficulty)
    }

    // Price range filter
    const priceRange = PRICE_RANGES[selectedPriceRange]
    filtered = filtered.filter(trip => trip.price_per_person >= priceRange.min && trip.price_per_person <= priceRange.max)

    // Sort
    switch (sortBy) {
      case 'price-low':
        filtered.sort((a, b) => a.price_per_person - b.price_per_person)
        break
      case 'price-high':
        filtered.sort((a, b) => b.price_per_person - a.price_per_person)
        break
      case 'duration':
        filtered.sort((a, b) => a.duration_days - b.duration_days)
        break
      default:
        // Keep original order for relevance
        break
    }

    return filtered
  }, [trips, searchQuery, selectedDifficulty, selectedPriceRange, sortBy])

  const handleSearch = (query: string) => {
    setSearchQuery(query)
    const params = new URLSearchParams(searchParams.toString())
    if (query) {
      params.set('q', query)
    } else {
      params.delete('q')
    }
    router.push(`/trips?${params.toString()}`)
  }

  const clearFilters = () => {
    setSelectedDifficulty('All')
    setSelectedPriceRange(0)
    setSortBy('relevance')
  }

  const hasActiveFilters = selectedDifficulty !== 'All' || selectedPriceRange !== 0

  return (
    <div className="space-y-8">
      {/* Search Header */}
      <div className="text-center">
        <h1 className="text-4xl font-bold text-gray-900 mb-4">
          {searchQuery ? `Search Results for "${searchQuery}"` : 'Explore Our Tours'}
        </h1>
        <p className="text-gray-600 mb-8">
          Discover amazing educational tours and adventures across India
        </p>

        {/* Search Bar */}
        <div className="max-w-2xl mx-auto relative">
          <Search className="absolute left-4 top-1/2 -translate-y-1/2 w-5 h-5 text-gray-400" />
          <input
            type="text"
            value={searchQuery}
            onChange={(e) => handleSearch(e.target.value)}
            placeholder="Search destinations, activities, or trip types..."
            className="w-full pl-12 pr-4 py-4 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent text-lg"
          />
        </div>
      </div>

      {/* Filters and Controls */}
      <div className="flex flex-col lg:flex-row gap-6">
        {/* Sidebar Filters */}
        <div className={`lg:w-80 ${showFilters ? 'block' : 'hidden lg:block'}`}>
          <div className="bg-white rounded-2xl p-6 shadow-lg sticky top-24">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-lg font-semibold text-gray-900">Filters</h3>
              {hasActiveFilters && (
                <button
                  onClick={clearFilters}
                  className="text-sm text-blue-600 hover:text-blue-700 flex items-center gap-1"
                >
                  <X className="w-4 h-4" />
                  Clear All
                </button>
              )}
            </div>

            <div className="space-y-6">
              {/* Difficulty Filter */}
              <div>
                <h4 className="font-medium text-gray-900 mb-3">Difficulty</h4>
                <div className="space-y-2">
                  {DIFFICULTIES.map((difficulty) => (
                    <label key={difficulty} className="flex items-center">
                      <input
                        type="radio"
                        name="difficulty"
                        value={difficulty}
                        checked={selectedDifficulty === difficulty}
                        onChange={(e) => setSelectedDifficulty(e.target.value)}
                        className="w-4 h-4 text-blue-600 border-gray-300 focus:ring-blue-500"
                      />
                      <span className="ml-3 text-gray-700 capitalize">{difficulty}</span>
                    </label>
                  ))}
                </div>
              </div>

              {/* Price Range Filter */}
              <div>
                <h4 className="font-medium text-gray-900 mb-3">Price Range</h4>
                <div className="space-y-2">
                  {PRICE_RANGES.map((range, index) => (
                    <label key={index} className="flex items-center">
                      <input
                        type="radio"
                        name="priceRange"
                        value={index}
                        checked={selectedPriceRange === index}
                        onChange={(e) => setSelectedPriceRange(parseInt(e.target.value))}
                        className="w-4 h-4 text-blue-600 border-gray-300 focus:ring-blue-500"
                      />
                      <span className="ml-3 text-gray-700">{range.label}</span>
                    </label>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="flex-1">
          {/* Results Header */}
          <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4 mb-6">
            <div className="flex items-center gap-4">
              <p className="text-gray-600">
                {filteredTrips.length} trip{filteredTrips.length !== 1 ? 's' : ''} found
              </p>

              {/* Mobile Filter Toggle */}
              <button
                onClick={() => setShowFilters(!showFilters)}
                className="lg:hidden flex items-center gap-2 px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors"
              >
                <SlidersHorizontal className="w-4 h-4" />
                Filters
              </button>
            </div>

            <div className="flex items-center gap-4">
              {/* Sort Dropdown */}
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value)}
                className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                {SORT_OPTIONS.map((option) => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>

              {/* View Mode Toggle */}
              <div className="flex bg-gray-100 rounded-lg p-1">
                <button
                  onClick={() => setViewMode('grid')}
                  className={`p-2 rounded ${viewMode === 'grid' ? 'bg-white shadow-sm' : ''}`}
                >
                  <Grid3X3 className="w-4 h-4" />
                </button>
                <button
                  onClick={() => setViewMode('list')}
                  className={`p-2 rounded ${viewMode === 'list' ? 'bg-white shadow-sm' : ''}`}
                >
                  <List className="w-4 h-4" />
                </button>
              </div>
            </div>
          </div>

          {/* Results Grid/List */}
          {loading ? (
            <div className="flex items-center justify-center py-12">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
            </div>
          ) : filteredTrips.length > 0 ? (
            <div className={`
              ${viewMode === 'grid'
                ? 'grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6'
                : 'space-y-6'
              }
            `}>
              {filteredTrips.map((trip, index) => (
                <TripCard
                  key={trip.id}
                  trip={trip}
                  viewMode={viewMode}
                  index={index}
                />
              ))}
            </div>
          ) : (
            <div className="text-center py-12">
              <div className="text-gray-400 mb-4">
                <Search className="w-16 h-16 mx-auto" />
              </div>
              <h3 className="text-xl font-medium text-gray-900 mb-2">No trips found</h3>
              <p className="text-gray-600 mb-6">
                Try adjusting your search criteria or filters
              </p>
              <Button onClick={clearFilters} variant="outline">
                Clear Filters
              </Button>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

// Trip Card Component
function TripCard({ trip, viewMode, index }: { trip: TripSearchResult; viewMode: 'grid' | 'list'; index: number }) {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: index * 0.1 }}
      className="group"
    >
      <Link href={`/trips/${trip.slug}`}>
        <div className={`
          bg-white rounded-xl overflow-hidden shadow-md hover:shadow-xl transition-shadow duration-300 border border-gray-200
          ${viewMode === 'list' ? 'flex gap-6' : ''}
        `}>
          {/* Trip Image */}
          <div className={`relative overflow-hidden ${
            viewMode === 'list' ? 'w-80 h-48 flex-shrink-0' : 'h-48'
          }`}>
            <Image
              src={trip.featured_image_url || '/images/fallback-trip.jpg'}
              alt={trip.title}
              fill
              className="object-cover transition-transform duration-300 group-hover:scale-105"
            />
            <div className="absolute top-3 left-3 flex gap-2">
              <span className="px-2 py-1 bg-blue-600 text-white text-xs font-medium rounded-full">
                {trip.destination}
              </span>
              <span className={`px-2 py-1 text-xs font-medium rounded-full capitalize ${
                trip.difficulty === 'easy' ? 'bg-green-100 text-green-800' :
                trip.difficulty === 'moderate' ? 'bg-yellow-100 text-yellow-800' :
                'bg-red-100 text-red-800'
              }`}>
                {trip.difficulty}
              </span>
            </div>
            {trip.is_featured && (
              <div className="absolute top-3 right-3">
                <div className="flex items-center gap-1 bg-white/90 backdrop-blur-sm px-2 py-1 rounded-full">
                  <Star className="w-3 h-3 fill-yellow-400 text-yellow-400" />
                  <span className="text-xs font-medium">Featured</span>
                </div>
              </div>
            )}
          </div>

          {/* Trip Content */}
          <div className={`p-6 ${viewMode === 'list' ? 'flex-1' : ''}`}>
            <div className="flex items-start justify-between mb-3">
              <div className="flex-1">
                <h3 className="text-xl font-bold text-gray-900 mb-1 group-hover:text-blue-600 transition-colors">
                  {trip.title}
                </h3>
                <div className="flex items-center gap-1 text-gray-600 mb-2">
                  <MapPin className="w-4 h-4" />
                  <span className="text-sm">{trip.destination}</span>
                </div>
              </div>
            </div>

            <p className="text-gray-600 text-sm mb-4 line-clamp-2">
              {trip.description || 'Discover amazing educational experiences and create unforgettable memories.'}
            </p>

            {/* Trip Details */}
            <div className="flex items-center gap-4 mb-4 text-sm text-gray-600">
              <div className="flex items-center gap-1">
                <Clock className="w-4 h-4" />
                <span>{trip.duration_days} Days</span>
              </div>
              <div className="flex items-center gap-1">
                <Users className="w-4 h-4" />
                <span>Educational Tour</span>
              </div>
            </div>

            {/* Price and CTA */}
            <div className="flex items-center justify-between">
              <div>
                <div className="text-2xl font-bold text-gray-900">
                  ₹{trip.price_per_person.toLocaleString()}
                </div>
                <div className="text-sm text-gray-600">per person</div>
              </div>
              <div className="text-right">
                <div className="text-sm text-blue-600 font-medium">
                  View Details
                </div>
              </div>
            </div>
          </div>
        </div>
      </Link>
    </motion.div>
  )
}
