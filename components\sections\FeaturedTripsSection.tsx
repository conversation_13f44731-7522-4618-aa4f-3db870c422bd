'use client';

import React, { useState } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { motion } from 'framer-motion';
import {
  MapPin,
  Calendar,
  Users,
  Star,
  ArrowRight,
  Filter,
  Mountain,
  Waves,
  TreePine,
  Building
} from 'lucide-react';
import { TRIP_CATEGORIES } from '@/lib/constants';
import { cn, getDifficultyColor } from '@/lib/utils';
import type { Trip } from '@/types/database';

const categoryIcons = {
  'Hill Station': Mountain,
  'Spiritual': Building,
  'Nature': TreePine,
  'Cultural': Building,
  'Religious': Building,
  'Trekking': Mountain,
  'Adventure': Waves,
  'Wildlife': TreePine,
  'Historical': Building,
};

interface FeaturedTrip {
  id: string;
  title: string;
  slug: string;
  description: string | null;
  destination: string;
  duration_days: number;
  price_per_person: number;
  difficulty: 'easy' | 'moderate' | 'challenging' | 'extreme';
  featured_image_url: string | null;
  is_featured: boolean;
  is_active: boolean;
}

interface FeaturedTripsSectionProps {
  featuredTrips: FeaturedTrip[];
}

export default function FeaturedTripsSection({ featuredTrips }: FeaturedTripsSectionProps) {
  const [selectedCategory, setSelectedCategory] = useState('All');
  const [hoveredTrip, setHoveredTrip] = useState<string | null>(null);

  // For now, we'll show all trips since we don't have category data in the database yet
  const filteredTrips = featuredTrips;

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 30 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
      },
    },
  };

  return (
    <section className="section-padding bg-gray-50">
      <div className="container-custom">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="text-center mb-16"
        >
          <h2 className="section-title">
            Featured <span className="text-gradient">Destinations</span>
          </h2>
          <p className="section-subtitle">
            Discover our most popular educational tours and adventure experiences.
            Each destination is carefully selected to provide meaningful learning opportunities
            and unforgettable memories.
          </p>
        </motion.div>

        {/* Category Filter */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.2 }}
          className="flex flex-wrap justify-center gap-3 mb-12"
        >
          <button
            onClick={() => setSelectedCategory('All')}
            className={cn(
              'px-6 py-3 rounded-full font-medium transition-all duration-300 flex items-center space-x-2',
              selectedCategory === 'All'
                ? 'bg-primary-600 text-white shadow-lg'
                : 'bg-white text-gray-700 hover:bg-primary-50 hover:text-primary-600 border border-gray-200'
            )}
          >
            <Filter className="h-4 w-4" />
            <span>All Destinations</span>
          </button>

          {TRIP_CATEGORIES.slice(0, 6).map((category) => {
            const IconComponent = categoryIcons[category as keyof typeof categoryIcons] || Mountain;
            return (
              <button
                key={category}
                onClick={() => setSelectedCategory(category)}
                className={cn(
                  'px-6 py-3 rounded-full font-medium transition-all duration-300 flex items-center space-x-2',
                  selectedCategory === category
                    ? 'bg-primary-600 text-white shadow-lg'
                    : 'bg-white text-gray-700 hover:bg-primary-50 hover:text-primary-600 border border-gray-200'
                )}
              >
                <IconComponent className="h-4 w-4" />
                <span>{category}</span>
              </button>
            );
          })}
        </motion.div>

        {/* Trips Grid */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
        >
          {filteredTrips.map((trip, index) => {
            const IconComponent = Mountain; // Default icon since we don't have category in database yet

            return (
              <motion.div
                key={trip.id}
                variants={itemVariants}
                onMouseEnter={() => setHoveredTrip(trip.id)}
                onMouseLeave={() => setHoveredTrip(null)}
                className="group"
              >
                <div className="card overflow-hidden hover:shadow-xl transition-all duration-500 transform hover:-translate-y-2">
                  {/* Image */}
                  <div className="relative h-64 overflow-hidden">
                    <Image
                      src={trip.featured_image_url || '/images/fallback-trip.jpg'}
                      alt={trip.title}
                      fill
                      className="object-cover transition-transform duration-700 group-hover:scale-110"
                    />

                    {/* Overlay */}
                    <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />

                    {/* Destination Badge */}
                    <div className="absolute top-4 left-4">
                      <div className="flex items-center space-x-2 bg-white/90 backdrop-blur-sm px-3 py-1 rounded-full">
                        <IconComponent className="h-4 w-4 text-primary-600" />
                        <span className="text-sm font-medium text-gray-800">{trip.destination}</span>
                      </div>
                    </div>

                    {/* Difficulty Badge */}
                    <div className="absolute top-4 right-4">
                      <span className={cn(
                        'px-3 py-1 rounded-full text-xs font-medium',
                        getDifficultyColor(trip.difficulty)
                      )}>
                        {trip.difficulty.charAt(0).toUpperCase() + trip.difficulty.slice(1)}
                      </span>
                    </div>

                    {/* Quick Info Overlay */}
                    <motion.div
                      initial={{ opacity: 0, y: 20 }}
                      animate={{
                        opacity: hoveredTrip === trip.id ? 1 : 0,
                        y: hoveredTrip === trip.id ? 0 : 20
                      }}
                      transition={{ duration: 0.3 }}
                      className="absolute bottom-4 left-4 right-4"
                    >
                      <div className="bg-white/90 backdrop-blur-sm rounded-lg p-3">
                        <div className="flex items-center justify-between text-sm">
                          <div className="flex items-center space-x-1 text-gray-700">
                            <Calendar className="h-4 w-4" />
                            <span>{trip.duration_days} Days</span>
                          </div>
                          <div className="flex items-center space-x-1 text-gray-700">
                            <span>₹{trip.price_per_person.toLocaleString()}</span>
                          </div>
                        </div>
                      </div>
                    </motion.div>
                  </div>

                  {/* Content */}
                  <div className="p-6">
                    <div className="flex items-start justify-between mb-3">
                      <h3 className="text-xl font-bold text-gray-900 group-hover:text-primary-600 transition-colors">
                        {trip.title}
                      </h3>
                      <div className="flex items-center space-x-1 text-yellow-500">
                        <Star className="h-4 w-4 fill-current" />
                        <span className="text-sm font-medium text-gray-700">4.8</span>
                      </div>
                    </div>

                    <p className="text-gray-600 mb-4 line-clamp-3 leading-relaxed">
                      {trip.description || 'Discover amazing educational experiences and create unforgettable memories.'}
                    </p>

                    {/* Trip Details */}
                    <div className="flex items-center justify-between mb-6 text-sm text-gray-500">
                      <div className="flex items-center space-x-1">
                        <Calendar className="h-4 w-4" />
                        <span>{trip.duration_days} Days</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <span className="font-semibold text-primary-600">₹{trip.price_per_person.toLocaleString()}</span>
                      </div>
                    </div>

                    {/* CTA */}
                    <Link
                      href={`/trips/${trip.slug}`}
                      className="inline-flex items-center justify-center w-full px-6 py-3 bg-primary-600 text-white font-semibold rounded-lg hover:bg-primary-700 transition-all duration-300 transform hover:scale-105 group"
                    >
                      Explore Trip
                      <ArrowRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform" />
                    </Link>
                  </div>
                </div>
              </motion.div>
            );
          })}
        </motion.div>

        {/* View All CTA */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.4 }}
          className="text-center mt-12"
        >
          <Link
            href="/trips"
            className="inline-flex items-center px-8 py-4 bg-gradient-to-r from-primary-600 to-secondary-600 text-white font-semibold rounded-lg hover:from-primary-700 hover:to-secondary-700 transition-all duration-300 transform hover:scale-105 shadow-lg group"
          >
            View All Destinations
            <ArrowRight className="ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform" />
          </Link>
        </motion.div>

        {/* Stats */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.6 }}
          className="grid grid-cols-2 md:grid-cols-4 gap-8 mt-16 pt-16 border-t border-gray-200"
        >
          <div className="text-center">
            <div className="text-3xl font-bold text-primary-600 mb-2">50+</div>
            <div className="text-gray-600">Destinations</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-primary-600 mb-2">1000+</div>
            <div className="text-gray-600">Happy Students</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-primary-600 mb-2">100+</div>
            <div className="text-gray-600">Schools Partnered</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-primary-600 mb-2">15+</div>
            <div className="text-gray-600">Years Experience</div>
          </div>
        </motion.div>
      </div>
    </section>
  );
}
