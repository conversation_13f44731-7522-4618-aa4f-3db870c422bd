"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/web-vitals";
exports.ids = ["vendor-chunks/web-vitals"];
exports.modules = {

/***/ "(ssr)/./node_modules/web-vitals/dist/web-vitals.js":
/*!****************************************************!*\
  !*** ./node_modules/web-vitals/dist/web-vitals.js ***!
  \****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CLSThresholds: () => (/* binding */ T),\n/* harmony export */   FCPThresholds: () => (/* binding */ b),\n/* harmony export */   INPThresholds: () => (/* binding */ N),\n/* harmony export */   LCPThresholds: () => (/* binding */ x),\n/* harmony export */   TTFBThresholds: () => (/* binding */ $),\n/* harmony export */   onCLS: () => (/* binding */ E),\n/* harmony export */   onFCP: () => (/* binding */ P),\n/* harmony export */   onINP: () => (/* binding */ S),\n/* harmony export */   onLCP: () => (/* binding */ O),\n/* harmony export */   onTTFB: () => (/* binding */ H)\n/* harmony export */ });\nlet e = -1;\nconst t = (t)=>{\n    addEventListener(\"pageshow\", (n)=>{\n        n.persisted && (e = n.timeStamp, t(n));\n    }, !0);\n}, n = (e, t, n, i)=>{\n    let o, s;\n    return (r)=>{\n        t.value >= 0 && (r || i) && (s = t.value - (o ?? 0), (s || void 0 === o) && (o = t.value, t.delta = s, t.rating = ((e, t)=>e > t[1] ? \"poor\" : e > t[0] ? \"needs-improvement\" : \"good\")(t.value, n), e(t)));\n    };\n}, i = (e)=>{\n    requestAnimationFrame(()=>requestAnimationFrame(()=>e()));\n}, o = ()=>{\n    const e = performance.getEntriesByType(\"navigation\")[0];\n    if (e && e.responseStart > 0 && e.responseStart < performance.now()) return e;\n}, s = ()=>{\n    const e = o();\n    return e?.activationStart ?? 0;\n}, r = (t, n = -1)=>{\n    const i = o();\n    let r = \"navigate\";\n    e >= 0 ? r = \"back-forward-cache\" : i && (document.prerendering || s() > 0 ? r = \"prerender\" : document.wasDiscarded ? r = \"restore\" : i.type && (r = i.type.replace(/_/g, \"-\")));\n    return {\n        name: t,\n        value: n,\n        rating: \"good\",\n        delta: 0,\n        entries: [],\n        id: `v5-${Date.now()}-${Math.floor(8999999999999 * Math.random()) + 1e12}`,\n        navigationType: r\n    };\n}, c = new WeakMap;\nfunction a(e, t) {\n    return c.get(e) || c.set(e, new t), c.get(e);\n}\nclass d {\n    h(e) {\n        if (e.hadRecentInput) return;\n        const t = this.o[0], n = this.o.at(-1);\n        this.i && t && n && e.startTime - n.startTime < 1e3 && e.startTime - t.startTime < 5e3 ? (this.i += e.value, this.o.push(e)) : (this.i = e.value, this.o = [\n            e\n        ]), this.t?.(e);\n    }\n    constructor(){\n        this.i = 0;\n        this.o = [];\n    }\n}\nconst h = (e, t, n = {})=>{\n    try {\n        if (PerformanceObserver.supportedEntryTypes.includes(e)) {\n            const i = new PerformanceObserver((e)=>{\n                Promise.resolve().then(()=>{\n                    t(e.getEntries());\n                });\n            });\n            return i.observe({\n                type: e,\n                buffered: !0,\n                ...n\n            }), i;\n        }\n    } catch  {}\n}, f = (e)=>{\n    let t = !1;\n    return ()=>{\n        t || (e(), t = !0);\n    };\n};\nlet u = -1;\nconst l = ()=>\"hidden\" !== document.visibilityState || document.prerendering ? 1 / 0 : 0, m = (e)=>{\n    \"hidden\" === document.visibilityState && u > -1 && (u = \"visibilitychange\" === e.type ? e.timeStamp : 0, p());\n}, g = ()=>{\n    addEventListener(\"visibilitychange\", m, !0), addEventListener(\"prerenderingchange\", m, !0);\n}, p = ()=>{\n    removeEventListener(\"visibilitychange\", m, !0), removeEventListener(\"prerenderingchange\", m, !0);\n}, v = ()=>{\n    if (u < 0) {\n        const e = s(), n = document.prerendering ? void 0 : globalThis.performance.getEntriesByType(\"visibility-state\").filter((t)=>\"hidden\" === t.name && t.startTime > e)[0]?.startTime;\n        u = n ?? l(), g(), t(()=>{\n            setTimeout(()=>{\n                u = l(), g();\n            });\n        });\n    }\n    return {\n        get firstHiddenTime () {\n            return u;\n        }\n    };\n}, y = (e)=>{\n    document.prerendering ? addEventListener(\"prerenderingchange\", ()=>e(), !0) : e();\n}, b = [\n    1800,\n    3e3\n], P = (e, o = {})=>{\n    y(()=>{\n        const c = v();\n        let a, d = r(\"FCP\");\n        const f = h(\"paint\", (e)=>{\n            for (const t of e)\"first-contentful-paint\" === t.name && (f.disconnect(), t.startTime < c.firstHiddenTime && (d.value = Math.max(t.startTime - s(), 0), d.entries.push(t), a(!0)));\n        });\n        f && (a = n(e, d, b, o.reportAllChanges), t((t)=>{\n            d = r(\"FCP\"), a = n(e, d, b, o.reportAllChanges), i(()=>{\n                d.value = performance.now() - t.timeStamp, a(!0);\n            });\n        }));\n    });\n}, T = [\n    .1,\n    .25\n], E = (e, o = {})=>{\n    P(f(()=>{\n        let s, c = r(\"CLS\", 0);\n        const f = a(o, d), u = (e)=>{\n            for (const t of e)f.h(t);\n            f.i > c.value && (c.value = f.i, c.entries = f.o, s());\n        }, l = h(\"layout-shift\", u);\n        l && (s = n(e, c, T, o.reportAllChanges), document.addEventListener(\"visibilitychange\", ()=>{\n            \"hidden\" === document.visibilityState && (u(l.takeRecords()), s(!0));\n        }), t(()=>{\n            f.i = 0, c = r(\"CLS\", 0), s = n(e, c, T, o.reportAllChanges), i(()=>s());\n        }), setTimeout(s));\n    }));\n};\nlet _ = 0, L = 1 / 0, M = 0;\nconst C = (e)=>{\n    for (const t of e)t.interactionId && (L = Math.min(L, t.interactionId), M = Math.max(M, t.interactionId), _ = M ? (M - L) / 7 + 1 : 0);\n};\nlet I;\nconst w = ()=>I ? _ : performance.interactionCount ?? 0, F = ()=>{\n    \"interactionCount\" in performance || I || (I = h(\"event\", C, {\n        type: \"event\",\n        buffered: !0,\n        durationThreshold: 0\n    }));\n};\nlet k = 0;\nclass A {\n    v() {\n        k = w(), this.u.length = 0, this.l.clear();\n    }\n    P() {\n        const e = Math.min(this.u.length - 1, Math.floor((w() - k) / 50));\n        return this.u[e];\n    }\n    h(e) {\n        if (this.m?.(e), !e.interactionId && \"first-input\" !== e.entryType) return;\n        const t = this.u.at(-1);\n        let n = this.l.get(e.interactionId);\n        if (n || this.u.length < 10 || e.duration > t.T) {\n            if (n ? e.duration > n.T ? (n.entries = [\n                e\n            ], n.T = e.duration) : e.duration === n.T && e.startTime === n.entries[0].startTime && n.entries.push(e) : (n = {\n                id: e.interactionId,\n                entries: [\n                    e\n                ],\n                T: e.duration\n            }, this.l.set(n.id, n), this.u.push(n)), this.u.sort((e, t)=>t.T - e.T), this.u.length > 10) {\n                const e = this.u.splice(10);\n                for (const t of e)this.l.delete(t.id);\n            }\n            this.p?.(n);\n        }\n    }\n    constructor(){\n        this.u = [];\n        this.l = new Map;\n    }\n}\nconst B = (e)=>{\n    const t = globalThis.requestIdleCallback || setTimeout;\n    \"hidden\" === document.visibilityState ? e() : (t(e = f(e)), document.addEventListener(\"visibilitychange\", e, {\n        once: !0\n    }));\n}, N = [\n    200,\n    500\n], S = (e, i = {})=>{\n    globalThis.PerformanceEventTiming && \"interactionId\" in PerformanceEventTiming.prototype && y(()=>{\n        F();\n        let o, s = r(\"INP\");\n        const c = a(i, A), d = (e)=>{\n            B(()=>{\n                for (const t of e)c.h(t);\n                const t = c.P();\n                t && t.T !== s.value && (s.value = t.T, s.entries = t.entries, o());\n            });\n        }, f = h(\"event\", d, {\n            durationThreshold: i.durationThreshold ?? 40\n        });\n        o = n(e, s, N, i.reportAllChanges), f && (f.observe({\n            type: \"first-input\",\n            buffered: !0\n        }), document.addEventListener(\"visibilitychange\", ()=>{\n            \"hidden\" === document.visibilityState && (d(f.takeRecords()), o(!0));\n        }), t(()=>{\n            c.v(), s = r(\"INP\"), o = n(e, s, N, i.reportAllChanges);\n        }));\n    });\n};\nclass q {\n    h(e) {\n        this.m?.(e);\n    }\n}\nconst x = [\n    2500,\n    4e3\n], O = (e, o = {})=>{\n    y(()=>{\n        const c = v();\n        let d, u = r(\"LCP\");\n        const l = a(o, q), m = (e)=>{\n            o.reportAllChanges || (e = e.slice(-1));\n            for (const t of e)l.h(t), t.startTime < c.firstHiddenTime && (u.value = Math.max(t.startTime - s(), 0), u.entries = [\n                t\n            ], d());\n        }, g = h(\"largest-contentful-paint\", m);\n        if (g) {\n            d = n(e, u, x, o.reportAllChanges);\n            const s = f(()=>{\n                m(g.takeRecords()), g.disconnect(), d(!0);\n            });\n            for (const e of [\n                \"keydown\",\n                \"click\",\n                \"visibilitychange\"\n            ])addEventListener(e, ()=>B(s), {\n                capture: !0,\n                once: !0\n            });\n            t((t)=>{\n                u = r(\"LCP\"), d = n(e, u, x, o.reportAllChanges), i(()=>{\n                    u.value = performance.now() - t.timeStamp, d(!0);\n                });\n            });\n        }\n    });\n}, $ = [\n    800,\n    1800\n], D = (e)=>{\n    document.prerendering ? y(()=>D(e)) : \"complete\" !== document.readyState ? addEventListener(\"load\", ()=>D(e), !0) : setTimeout(e);\n}, H = (e, i = {})=>{\n    let c = r(\"TTFB\"), a = n(e, c, $, i.reportAllChanges);\n    D(()=>{\n        const d = o();\n        d && (c.value = Math.max(d.responseStart - s(), 0), c.entries = [\n            d\n        ], a(!0), t(()=>{\n            c = r(\"TTFB\", 0), a = n(e, c, $, i.reportAllChanges), a(!0);\n        }));\n    });\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/web-vitals/dist/web-vitals.js\n");

/***/ })

};
;